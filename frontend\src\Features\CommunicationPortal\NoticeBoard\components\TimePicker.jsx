import { useState, useRef, useEffect } from 'react';
import { FaClock, FaChevronDown } from 'react-icons/fa';

const TimePicker = ({ value, onChange, placeholder = "Select Time" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHour, setSelectedHour] = useState('');
  const [selectedMinute, setSelectedMinute] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('AM');
  const [dropdownPosition, setDropdownPosition] = useState('bottom');
  const timePickerRef = useRef(null);
  const inputRef = useRef(null);

  // Initialize from value
  useEffect(() => {
    if (value) {
      const [hours, minutes] = value.split(':');
      const hour24 = parseInt(hours);
      const minute = minutes;
      
      let hour12 = hour24;
      let period = 'AM';
      
      if (hour24 === 0) {
        hour12 = 12;
        period = 'AM';
      } else if (hour24 < 12) {
        hour12 = hour24;
        period = 'AM';
      } else if (hour24 === 12) {
        hour12 = 12;
        period = 'PM';
      } else {
        hour12 = hour24 - 12;
        period = 'PM';
      }
      
      setSelectedHour(hour12.toString());
      setSelectedMinute(minute);
      setSelectedPeriod(period);
    }
  }, [value]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (timePickerRef.current && !timePickerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Calculate dropdown position
  useEffect(() => {
    if (isOpen && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      
      if (spaceBelow < 300 && spaceAbove > 300) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [isOpen]);

  const formatTime = (hour, minute, period) => {
    if (!hour || !minute) return '';
    return `${hour}:${minute} ${period}`;
  };

  const handleTimeSelect = (hour, minute, period) => {
    setSelectedHour(hour);
    setSelectedMinute(minute);
    setSelectedPeriod(period);

    // Convert to 24-hour format for form submission
    let hour24 = parseInt(hour);
    if (period === 'PM' && hour24 !== 12) {
      hour24 += 12;
    } else if (period === 'AM' && hour24 === 12) {
      hour24 = 0;
    }

    const timeString = `${hour24.toString().padStart(2, '0')}:${minute}`;
    onChange(timeString);
    setIsOpen(false);
  };

  // Generate hours (1-12)
  const hours = Array.from({ length: 12 }, (_, i) => (i + 1).toString());
  
  // Generate minutes (00, 15, 30, 45)
  const minutes = ['00', '15', '30', '45'];
  
  // Periods
  const periods = ['AM', 'PM'];

  return (
    <div className="relative" ref={timePickerRef}>
      {/* Input Field */}
      <div
        ref={inputRef}
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white cursor-pointer flex items-center justify-between"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={selectedHour && selectedMinute ? 'text-gray-900 text-sm' : 'text-gray-500 text-sm'}>
          {selectedHour && selectedMinute
            ? formatTime(selectedHour, selectedMinute, selectedPeriod)
            : placeholder
          }
        </span>
        <FaChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className={`absolute z-50 w-full bg-white border border-gray-300 rounded-lg shadow-lg ${
          dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'
        }`}>
          <div className="p-4">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <FaClock className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Select Time</span>
            </div>
            
            <div className="grid grid-cols-3 gap-2">
              {/* Hours */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Hour</label>
                <div className="max-h-32 overflow-y-auto border border-gray-200 rounded">
                  {hours.map((hour) => (
                    <button
                      key={hour}
                      type="button"
                      onClick={() => {
                        setSelectedHour(hour);
                        if (selectedMinute && selectedPeriod) {
                          handleTimeSelect(hour, selectedMinute, selectedPeriod);
                        }
                      }}
                      className={`w-full px-2 py-1 text-sm text-left hover:bg-gray-50 ${
                        selectedHour === hour ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                      }`}
                    >
                      {hour}
                    </button>
                  ))}
                </div>
              </div>

              {/* Minutes */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Minute</label>
                <div className="max-h-32 overflow-y-auto border border-gray-200 rounded">
                  {minutes.map((minute) => (
                    <button
                      key={minute}
                      type="button"
                      onClick={() => {
                        setSelectedMinute(minute);
                        if (selectedHour && selectedPeriod) {
                          handleTimeSelect(selectedHour, minute, selectedPeriod);
                        }
                      }}
                      className={`w-full px-2 py-1 text-sm text-left hover:bg-gray-50 ${
                        selectedMinute === minute ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                      }`}
                    >
                      {minute}
                    </button>
                  ))}
                </div>
              </div>

              {/* Period */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Period</label>
                <div className="border border-gray-200 rounded">
                  {periods.map((period) => (
                    <button
                      key={period}
                      type="button"
                      onClick={() => {
                        setSelectedPeriod(period);
                        if (selectedHour && selectedMinute) {
                          handleTimeSelect(selectedHour, selectedMinute, period);
                        }
                      }}
                      className={`w-full px-2 py-1 text-sm text-left hover:bg-gray-50 ${
                        selectedPeriod === period ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                      }`}
                    >
                      {period}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Current Selection */}
            {selectedHour && selectedMinute && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="text-center">
                  <span className="text-sm text-gray-600">Selected: </span>
                  <span className="text-sm font-medium text-blue-600">
                    {formatTime(selectedHour, selectedMinute, selectedPeriod)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TimePicker;
