import { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaSpinner } from "react-icons/fa";
import FileUpload from "../components/FileUpload";
import PostAsSelector from "../components/PostAsSelector";
import PriorityDropdown from "../components/PriorityDropdown";
import LabelSelector from "../components/LabelSelector";
import TowerUnitSelector from "../components/TowerUnitSelector";
import TimePicker from "../components/TimePicker";
import UserCountDisplay from "../components/UserCountDisplay";
import { useCurrentUser } from "../hooks/useCurrentUser";

// Validation schema for notice form
const validationSchema = Yup.object({
  priority: Yup.string().required("Priority is required"),
  label: Yup.string(),
  startDate: Yup.date().required("Start date is required"),
  startTime: Yup.string().required("Start time is required"),
  endDate: Yup.date()
    .required("End date is required")
    .min(Yup.ref('startDate'), "End date must be after start date"),
  endTime: Yup.string().required("End time is required"),
  postAs: Yup.string().required("Post as selection is required"),
  postedGroup: Yup.number().when('postAs', {
    is: 'group',
    then: (schema) => schema.required("Group selection is required when posting as group"),
    otherwise: (schema) => schema.nullable()
  }),
  postedMember: Yup.number().when('postAs', {
    is: 'member',
    then: (schema) => schema.required("Member selection is required when posting as member"),
    otherwise: (schema) => schema.nullable()
  }),
  targetTowers: Yup.array().min(1, "At least one tower must be selected"),
  targetUnits: Yup.array(), // Units are optional
  attachments: Yup.array().min(1, "At least one image is required")
});

const EditNoticeForm = ({ notice, onSubmit, onPreview, loading }) => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [existingAttachments, setExistingAttachments] = useState([]);
  const [selectedTowers, setSelectedTowers] = useState([]);
  const [selectedUnits, setSelectedUnits] = useState([]);
  const { currentUser } = useCurrentUser();

  // Initialize form with notice data
  const formik = useFormik({
    initialValues: {
      priority: notice?.priority || "normal",
      label: notice?.label || "",
      startDate: notice?.start_date || notice?.startDate || "",
      startTime: notice?.start_time || notice?.startTime || "",
      endDate: notice?.end_date || notice?.endDate || "",
      endTime: notice?.end_time || notice?.endTime || "",
      postAs: notice?.post_as || notice?.postAs || "creator",
      postedGroup: notice?.posted_group || notice?.postedGroup || null,
      postedMember: notice?.posted_member || notice?.postedMember || null,
      targetTowers: [],
      targetUnits: [],
      attachments: []
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      const formData = new FormData();
      
      // Add basic fields
      formData.append('priority', values.priority);
      if (values.label) formData.append('label', values.label);
      formData.append('start_date', values.startDate);
      formData.append('start_time', values.startTime);
      formData.append('end_date', values.endDate);
      formData.append('end_time', values.endTime);
      formData.append('post_as', values.postAs);
      
      // Add post as specific fields
      if (values.postAs === 'group' && values.postedGroup) {
        formData.append('posted_group', values.postedGroup);
      }
      if (values.postAs === 'member' && values.postedMember) {
        formData.append('posted_member', values.postedMember);
      }
      
      // Add target towers and units
      values.targetTowers.forEach(towerId => {
        formData.append('target_tower_ids', towerId);
      });
      values.targetUnits.forEach(unitId => {
        formData.append('target_unit_ids', unitId);
      });
      
      // Add new attachments
      uploadedFiles.forEach((file, index) => {
        formData.append('attachments', file);
      });

      // Add existing attachment IDs to keep
      existingAttachments.forEach(attachment => {
        formData.append('existing_attachment_ids', attachment.id);
      });

      onSubmit(formData);
    }
  });

  // Initialize data from notice
  useEffect(() => {
    if (notice) {
      // Set existing attachments
      setExistingAttachments(notice.attachments || []);
      
      // Set selected towers and units
      const towerIds = notice.target_towers_data?.map(tower => tower.id) || [];
      const unitIds = notice.target_units_data?.map(unit => unit.id) || [];
      setSelectedTowers(towerIds);
      setSelectedUnits(unitIds);
    }
  }, [notice]);

  // Update formik values when external state changes
  useEffect(() => {
    formik.setFieldValue('targetTowers', selectedTowers);
  }, [selectedTowers]);

  useEffect(() => {
    formik.setFieldValue('targetUnits', selectedUnits);
  }, [selectedUnits]);

  useEffect(() => {
    const totalAttachments = existingAttachments.length + uploadedFiles.length;
    formik.setFieldValue('attachments', totalAttachments > 0 ? ['dummy'] : []);
  }, [uploadedFiles, existingAttachments]);

  const handlePreview = () => {
    const previewData = {
      ...formik.values,
      existingAttachments,
      attachments: [
        ...existingAttachments.map(att => ({
          id: att.id,
          preview: att.file_url,
          name: att.file_name,
          isExisting: true
        })),
        ...uploadedFiles.map(file => ({
          file,
          preview: URL.createObjectURL(file),
          name: file.name,
          isExisting: false
        }))
      ]
    };
    onPreview(previewData);
  };

  const handleFileUpload = (files) => {
    setUploadedFiles(files);
  };

  const handleTowerUnitChange = (towers, units) => {
    setSelectedTowers(towers);
    setSelectedUnits(units);
  };

  const handleRemoveExistingAttachment = (attachmentId) => {
    setExistingAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const totalAttachments = existingAttachments.length + uploadedFiles.length;
  const isFormValid = formik.isValid && totalAttachments > 0 && selectedTowers.length > 0;

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      {/* File Upload Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Images <span className="text-red-500">*</span>
        </label>
        <FileUpload
          onFilesChange={handleFileUpload}
          maxFiles={10}
          acceptedTypes={['image/*']}
          existingAttachments={existingAttachments}
          onRemoveExisting={handleRemoveExistingAttachment}
          error={formik.touched.attachments && formik.errors.attachments}
        />
        {formik.touched.attachments && formik.errors.attachments && (
          <p className="mt-1 text-sm text-red-600">{formik.errors.attachments}</p>
        )}
        <p className="mt-1 text-sm text-gray-500">
          Total images: {totalAttachments} ({existingAttachments.length} existing, {uploadedFiles.length} new)
        </p>
      </div>

      {/* Priority and Label */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Priority <span className="text-red-500">*</span>
          </label>
          <PriorityDropdown
            value={formik.values.priority}
            onChange={(value) => formik.setFieldValue('priority', value)}
            error={formik.touched.priority && formik.errors.priority}
          />
          {formik.touched.priority && formik.errors.priority && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.priority}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Label
          </label>
          <LabelSelector
            value={formik.values.label}
            onChange={(value) => formik.setFieldValue('label', value)}
            error={formik.touched.label && formik.errors.label}
          />
        </div>
      </div>

      {/* Date and Time */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Start Date & Time <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 gap-2">
            <input
              type="date"
              name="startDate"
              value={formik.values.startDate}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <TimePicker
              value={formik.values.startTime}
              onChange={(value) => formik.setFieldValue('startTime', value)}
            />
          </div>
          {((formik.touched.startDate && formik.errors.startDate) || 
            (formik.touched.startTime && formik.errors.startTime)) && (
            <p className="mt-1 text-sm text-red-600">
              {formik.errors.startDate || formik.errors.startTime}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            End Date & Time <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 gap-2">
            <input
              type="date"
              name="endDate"
              value={formik.values.endDate}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <TimePicker
              value={formik.values.endTime}
              onChange={(value) => formik.setFieldValue('endTime', value)}
            />
          </div>
          {((formik.touched.endDate && formik.errors.endDate) || 
            (formik.touched.endTime && formik.errors.endTime)) && (
            <p className="mt-1 text-sm text-red-600">
              {formik.errors.endDate || formik.errors.endTime}
            </p>
          )}
        </div>
      </div>

      {/* Post As */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Post As <span className="text-red-500">*</span>
        </label>
        <PostAsSelector
          value={formik.values.postAs}
          selectedGroup={formik.values.postedGroup}
          selectedMember={formik.values.postedMember}
          onChange={(postAs, groupId, memberId) => {
            formik.setFieldValue('postAs', postAs);
            formik.setFieldValue('postedGroup', groupId);
            formik.setFieldValue('postedMember', memberId);
          }}
          currentUser={currentUser}
          error={formik.touched.postAs && formik.errors.postAs}
        />
        {formik.touched.postAs && formik.errors.postAs && (
          <p className="mt-1 text-sm text-red-600">{formik.errors.postAs}</p>
        )}
      </div>

      {/* Tower and Unit Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Target Audience <span className="text-red-500">*</span>
        </label>
        <TowerUnitSelector
          selectedTowers={selectedTowers}
          selectedUnits={selectedUnits}
          onChange={handleTowerUnitChange}
          error={formik.touched.targetTowers && formik.errors.targetTowers}
        />
        {formik.touched.targetTowers && formik.errors.targetTowers && (
          <p className="mt-1 text-sm text-red-600">{formik.errors.targetTowers}</p>
        )}
      </div>

      {/* User Count Display */}
      {selectedUnits.length > 0 && (
        <UserCountDisplay unitIds={selectedUnits} />
      )}

      {/* Action Buttons */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="text-sm text-gray-500">
          <span className="text-red-500">*</span> Required fields
        </div>
        
        <div className="flex gap-3">
          <button
            type="button"
            onClick={handlePreview}
            disabled={!isFormValid || loading}
            className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FaEye className="w-4 h-4" />
            Preview
          </button>
          
          <button
            type="submit"
            disabled={!isFormValid || loading}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <FaSpinner className="w-4 h-4 animate-spin" />
            ) : (
              <FaSave className="w-4 h-4" />
            )}
            {loading ? "Updating..." : "Update Notice"}
          </button>
        </div>
      </div>
    </form>
  );
};

export default EditNoticeForm;
