import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaPlus, FaImage } from "react-icons/fa";
import { <PERSON>i<PERSON><PERSON>ch, BiFilter } from "react-icons/bi";
import NoticeHistoryModal from "../components/NoticeHistoryModal";
import usePinPost from "../components/PinPost";
import Calendar from "../components/Calendar";
import ConfirmationMessageBox from "../../../../Components/MessageBox/ConfirmationMessageBox";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import ImageSlider from "../../../../Components/Modal/ImageSlider";
import { useNotices } from "../../../../hooks/useNotices";
import NoticeListPreview from "./NoticeListPreview";
import FilterSelectModal from "../../../../Components/FilterSelect/FilterSelectModal";
import { clearUserCountCache } from "../hooks/useUserCount";

const NoticeList = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Redux hooks for notices
  const {
    notices: reduxNotices,
    loading: reduxLoading,
    deleteSuccess,
    message,
    loadNotices: loadNoticesRedux,
    updateAllStatuses: updateAllStatusesRedux,
    removeNotice: removeNoticeRedux,
    moveNoticeToExpired: moveNoticeToExpiredRedux,
    restoreExpiredNotice: restoreExpiredNoticeRedux,
    loadNotice: loadNoticeRedux,
    clearAllSuccess: clearAllSuccessRedux
  } = useNotices();

  const [activeTab, setActiveTab] = useState(() => {
    return (
      parseInt(localStorage.getItem("noticeActiveTab")) ||
      location.state?.activeTab ||
      1
    );
  });
  const [myPostChecked, setMyPostChecked] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState([]);
  const [selectedLabel, setSelectedLabel] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [availableLabels, setAvailableLabels] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageSliderOpen, setIsImageSliderOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [isDocumentViewerOpen, setIsDocumentViewerOpen] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [noticeToDelete, setNoticeToDelete] = useState(null);
  const [showExpireConfirmation, setShowExpireConfirmation] = useState(false);
  const [noticeToExpire, setNoticeToExpire] = useState(null);
  const [showRestoreConfirmation, setShowRestoreConfirmation] = useState(false);
  const [noticeToRestore, setNoticeToRestore] = useState(null);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [selectedNoticeHistory, setSelectedNoticeHistory] = useState(null);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [filteredNotices, setFilteredNotices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const { pinPost, unpinPost } = usePinPost();
  const searchInputRef = useRef(null);

  // Load notices on component mount and when activeTab changes
  useEffect(() => {
    loadNotices();
  }, [activeTab]);

  // Handle success messages from Redux
  useEffect(() => {
    if (deleteSuccess && message) {
      setSuccessMessage(message);
      setShowSuccessMessage(true);
      clearAllSuccessRedux();
    }
  }, [deleteSuccess, message, clearAllSuccessRedux]);

  // Save active tab to localStorage
  useEffect(() => {
    localStorage.setItem("noticeActiveTab", activeTab.toString());
  }, [activeTab]);

  // Clear user count cache when component unmounts
  useEffect(() => {
    return () => {
      clearUserCountCache();
    };
  }, []);

  const loadNotices = async () => {
    setIsLoading(true);
    try {
      await loadNoticesRedux();
    } catch (error) {
      console.error("Error loading notices:", error);
      setErrorMessage("Failed to load notices. Please try again.");
      setShowErrorMessage(true);
    } finally {
      setIsLoading(false);
    }
  };

  const updateAllStatuses = async () => {
    try {
      await updateAllStatusesRedux();
      await loadNotices(); // Reload notices after status update
    } catch (error) {
      console.error("Error updating statuses:", error);
      setErrorMessage("Failed to update notice statuses. Please try again.");
      setShowErrorMessage(true);
    }
  };

  // Filter notices based on active tab, search term, priority, label, and date
  useEffect(() => {
    let filtered = reduxNotices || [];

    // Filter by status based on active tab
    if (activeTab === 1) {
      filtered = filtered.filter((notice) => notice.status === "ongoing");
    } else if (activeTab === 2) {
      filtered = filtered.filter((notice) => notice.status === "upcoming");
    } else if (activeTab === 3) {
      filtered = filtered.filter((notice) => notice.status === "expired");
    }

    // Filter by search term (search in internal title and author)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (notice) =>
          (notice.internalTitle && notice.internalTitle.toLowerCase().includes(searchLower)) ||
          (notice.author && notice.author.toLowerCase().includes(searchLower))
      );
    }

    // Filter by priority
    if (selectedPriority.length > 0) {
      filtered = filtered.filter((notice) =>
        selectedPriority.includes(notice.priority)
      );
    }

    // Filter by label
    if (selectedLabel.length > 0) {
      filtered = filtered.filter((notice) =>
        selectedLabel.includes(notice.label)
      );
    }

    // Filter by date
    if (selectedDate) {
      const selectedDateStr = selectedDate.toISOString().split("T")[0];
      filtered = filtered.filter((notice) => {
        const noticeStartDate = new Date(notice.startDate)
          .toISOString()
          .split("T")[0];
        const noticeEndDate = new Date(notice.endDate)
          .toISOString()
          .split("T")[0];
        return (
          selectedDateStr >= noticeStartDate && selectedDateStr <= noticeEndDate
        );
      });
    }

    // Filter by "My Posts" if checked
    if (myPostChecked) {
      // This would need to be implemented based on current user
      // For now, we'll skip this filter
    }

    setFilteredNotices(filtered);
  }, [
    reduxNotices,
    activeTab,
    searchTerm,
    selectedPriority,
    selectedLabel,
    selectedDate,
    myPostChecked,
  ]);

  // Extract unique labels from notices for filter options
  useEffect(() => {
    const labels = [...new Set(reduxNotices?.map((notice) => notice.label).filter(Boolean))];
    setAvailableLabels(labels);
  }, [reduxNotices]);

  const handleTabChange = (tabNumber) => {
    setActiveTab(tabNumber);
  };

  const handleAddNotice = () => {
    navigate("/communication-portal/notice-board/add");
  };

  const handleEditNotice = (noticeId) => {
    navigate(`/communication-portal/notice-board/edit/${noticeId}`);
  };

  const handleDeleteNotice = (notice) => {
    setNoticeToDelete(notice);
    setShowDeleteConfirmation(true);
  };

  const confirmDeleteNotice = async () => {
    if (noticeToDelete) {
      try {
        await removeNoticeRedux(noticeToDelete.id);
        setShowDeleteConfirmation(false);
        setNoticeToDelete(null);
      } catch (error) {
        console.error("Error deleting notice:", error);
        setErrorMessage("Failed to delete notice. Please try again.");
        setShowErrorMessage(true);
      }
    }
  };

  const handleExpireNotice = (notice) => {
    setNoticeToExpire(notice);
    setShowExpireConfirmation(true);
  };

  const confirmExpireNotice = async () => {
    if (noticeToExpire) {
      try {
        await moveNoticeToExpiredRedux(noticeToExpire.id);
        setShowExpireConfirmation(false);
        setNoticeToExpire(null);
        setSuccessMessage("Notice has been moved to expired.");
        setShowSuccessMessage(true);
      } catch (error) {
        console.error("Error expiring notice:", error);
        setErrorMessage("Failed to expire notice. Please try again.");
        setShowErrorMessage(true);
      }
    }
  };

  const handleRestoreNotice = (notice) => {
    setNoticeToRestore(notice);
    setShowRestoreConfirmation(true);
  };

  const confirmRestoreNotice = async () => {
    if (noticeToRestore) {
      try {
        await restoreExpiredNoticeRedux(noticeToRestore.id);
        setShowRestoreConfirmation(false);
        setNoticeToRestore(null);
        setSuccessMessage("Notice has been restored.");
        setShowSuccessMessage(true);
      } catch (error) {
        console.error("Error restoring notice:", error);
        setErrorMessage("Failed to restore notice. Please try again.");
        setShowErrorMessage(true);
      }
    }
  };

  const handlePinToggle = async (notice) => {
    try {
      if (notice.isPinned) {
        await unpinPost(notice.id);
      } else {
        await pinPost(notice.id);
      }
      await loadNotices(); // Reload to get updated pin status
    } catch (error) {
      console.error("Error toggling pin:", error);
      setErrorMessage("Failed to update pin status. Please try again.");
      setShowErrorMessage(true);
    }
  };

  const handleImageClick = (images, index) => {
    setSelectedImages(images);
    setSelectedImageIndex(index);
    setIsImageSliderOpen(true);
  };

  const handleViewHistory = async (notice) => {
    try {
      const fullNotice = await loadNoticeRedux(notice.id);
      setSelectedNoticeHistory(fullNotice);
      setShowHistoryModal(true);
    } catch (error) {
      console.error("Error loading notice history:", error);
      setErrorMessage("Failed to load notice history. Please try again.");
      setShowErrorMessage(true);
    }
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedPriority([]);
    setSelectedLabel([]);
    setSelectedDate(null);
    setMyPostChecked(false);
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
  };

  const handleFilterApply = (filters) => {
    setSelectedPriority(filters.priority || []);
    setSelectedLabel(filters.label || []);
    setMyPostChecked(filters.myPosts || false);
    setShowFilterModal(false);
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
    setShowCalendar(false);
  };

  const getTabCounts = () => {
    const ongoing = reduxNotices?.filter((notice) => notice.status === "ongoing").length || 0;
    const upcoming = reduxNotices?.filter((notice) => notice.status === "upcoming").length || 0;
    const expired = reduxNotices?.filter((notice) => notice.status === "expired").length || 0;
    return { ongoing, upcoming, expired };
  };

  const tabCounts = getTabCounts();

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Notice Board</h1>
          <p className="text-gray-600 mt-1">Manage and view image notices</p>
        </div>
        <button
          onClick={handleAddNotice}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <FaPlus className="w-4 h-4" />
          Add Notice
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1 relative">
            <BiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search notices..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filter Buttons */}
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilterModal(true)}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center gap-2"
            >
              <BiFilter className="w-4 h-4" />
              Filter
            </button>
            <button
              onClick={() => setShowCalendar(true)}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Date
            </button>
            <button
              onClick={handleClearFilters}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Clear All
            </button>
            <button
              onClick={updateAllStatuses}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg"
            >
              Update Status
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => handleTabChange(1)}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 1
                ? "border-blue-500 text-blue-600 bg-blue-50"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            Ongoing ({tabCounts.ongoing})
          </button>
          <button
            onClick={() => handleTabChange(2)}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 2
                ? "border-blue-500 text-blue-600 bg-blue-50"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            Upcoming ({tabCounts.upcoming})
          </button>
          <button
            onClick={() => handleTabChange(3)}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 3
                ? "border-blue-500 text-blue-600 bg-blue-50"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            Expired ({tabCounts.expired})
          </button>
        </div>
      </div>

      {/* Notice List */}
      <NoticeListPreview
        notices={filteredNotices}
        loading={isLoading || reduxLoading}
        onEdit={handleEditNotice}
        onDelete={handleDeleteNotice}
        onExpire={handleExpireNotice}
        onRestore={handleRestoreNotice}
        onPinToggle={handlePinToggle}
        onImageClick={handleImageClick}
        onViewHistory={handleViewHistory}
        activeTab={activeTab}
      />

      {/* Modals and Dialogs */}
      {showDeleteConfirmation && (
        <ConfirmationMessageBox
          message={`Are you sure you want to delete this notice?`}
          onConfirm={confirmDeleteNotice}
          onCancel={() => {
            setShowDeleteConfirmation(false);
            setNoticeToDelete(null);
          }}
        />
      )}

      {showExpireConfirmation && (
        <ConfirmationMessageBox
          message={`Are you sure you want to move this notice to expired?`}
          onConfirm={confirmExpireNotice}
          onCancel={() => {
            setShowExpireConfirmation(false);
            setNoticeToExpire(null);
          }}
        />
      )}

      {showRestoreConfirmation && (
        <ConfirmationMessageBox
          message={`Are you sure you want to restore this notice?`}
          onConfirm={confirmRestoreNotice}
          onCancel={() => {
            setShowRestoreConfirmation(false);
            setNoticeToRestore(null);
          }}
        />
      )}

      {showSuccessMessage && (
        <MessageBox
          message={successMessage}
          type="success"
          onClose={() => setShowSuccessMessage(false)}
        />
      )}

      {showErrorMessage && (
        <MessageBox
          message={errorMessage}
          type="error"
          onClose={() => setShowErrorMessage(false)}
        />
      )}

      {isImageSliderOpen && (
        <ImageSlider
          images={selectedImages}
          initialIndex={selectedImageIndex}
          onClose={() => setIsImageSliderOpen(false)}
        />
      )}

      {showHistoryModal && selectedNoticeHistory && (
        <NoticeHistoryModal
          notice={selectedNoticeHistory}
          onClose={() => {
            setShowHistoryModal(false);
            setSelectedNoticeHistory(null);
          }}
        />
      )}

      {showFilterModal && (
        <FilterSelectModal
          isOpen={showFilterModal}
          onClose={() => setShowFilterModal(false)}
          onApply={handleFilterApply}
          availableLabels={availableLabels}
          currentFilters={{
            priority: selectedPriority,
            label: selectedLabel,
            myPosts: myPostChecked,
          }}
        />
      )}

      {showCalendar && (
        <Calendar
          onDateSelect={handleDateSelect}
          onClose={() => setShowCalendar(false)}
          selectedDate={selectedDate}
        />
      )}
    </div>
  );
};

export default NoticeList;
