"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path,include
from django.http import HttpResponse
from django.conf import settings
from django.conf.urls.static import static

def default_page(request):
    html = """
    <html>
        <head>
            <title>Estate Page</title>
        </head>
        <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 50px;">
            <h1 style="color: #4CAF50;">Welcome to Estate Link Django Dashboard </h1>
        </body>
    </html>
    """
    return HttpResponse(html)

urlpatterns = [
    path('', default_page),
    path('admin/', admin.site.urls),
    path('user/', include('user.urls')),
    path('group_role/', include('group_role.urls')),
    path('towers/', include('towers.urls')),
    path('api/', include('announcements.urls')),
    path('api/', include('bulletins.urls')),
    path('api/', include('noticeboard.urls')),

]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
