import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FaArrowLeft, FaEye } from "react-icons/fa";
import AddNoticeForm from "./AddNoticeForm";
import NoticePreview from "../components/NoticePreview";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import { useNotices } from "../../../../hooks/useNotices";

const AddNotice = () => {
  const navigate = useNavigate();
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const {
    creating,
    createSuccess,
    createError,
    message,
    createNotice: createNoticeRedux,
    clearAllSuccess: clearAllSuccessRedux,
    clearErrors: clearErrorsRedux
  } = useNotices();

  // Handle success
  useEffect(() => {
    if (createSuccess && message) {
      setShowSuccessMessage(true);
      // Navigate back to notice list after a short delay
      setTimeout(() => {
        navigate("/communication-portal/notice-board", {
          state: { activeTab: 1 } // Go to ongoing tab
        });
      }, 2000);
    }
  }, [createSuccess, message, navigate]);

  // Handle errors
  useEffect(() => {
    if (createError) {
      setErrorMessage(
        typeof createError === 'string' 
          ? createError 
          : createError.message || "Failed to create notice. Please try again."
      );
      setShowErrorMessage(true);
    }
  }, [createError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllSuccessRedux();
      clearErrorsRedux();
    };
  }, [clearAllSuccessRedux, clearErrorsRedux]);

  const handleBack = () => {
    navigate("/communication-portal/notice-board");
  };

  const handlePreview = (formData) => {
    setPreviewData(formData);
    setShowPreview(true);
  };

  const handleSubmit = async (formData) => {
    try {
      await createNoticeRedux(formData);
    } catch (error) {
      console.error("Error creating notice:", error);
      setErrorMessage("Failed to create notice. Please try again.");
      setShowErrorMessage(true);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setPreviewData(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              disabled={creating}
            >
              <FaArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Add New Notice</h1>
              <p className="text-gray-600 mt-1">Create a new image notice for the community</p>
            </div>
          </div>
          
          {previewData && (
            <button
              onClick={() => setShowPreview(true)}
              className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
              disabled={creating}
            >
              <FaEye className="w-4 h-4" />
              Preview
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <AddNoticeForm
                onSubmit={handleSubmit}
                onPreview={handlePreview}
                loading={creating}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && previewData && (
        <NoticePreview
          data={previewData}
          onClose={handleClosePreview}
          onSubmit={handleSubmit}
          loading={creating}
        />
      )}

      {/* Success Message */}
      {showSuccessMessage && (
        <MessageBox
          message={message || "Notice has been successfully created!"}
          type="success"
          onClose={() => setShowSuccessMessage(false)}
        />
      )}

      {/* Error Message */}
      {showErrorMessage && (
        <MessageBox
          message={errorMessage}
          type="error"
          onClose={() => {
            setShowErrorMessage(false);
            clearErrorsRedux();
          }}
        />
      )}
    </div>
  );
};

export default AddNotice;
