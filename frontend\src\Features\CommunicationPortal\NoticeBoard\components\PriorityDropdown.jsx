import { useState, useRef, useEffect } from 'react';
import { FaFlag, FaChevronDown, FaTimes } from 'react-icons/fa';

const priorities = [
  { value: 'low', label: 'Low', color: 'text-gray-600' },
  { value: 'normal', label: 'Normal', color: 'text-blue-600' },
  { value: 'high', label: 'High', color: 'text-orange-600' },
  { value: 'urgent', label: 'Urgent', color: 'text-red-600' }
];

const getPriorityColor = (priority) => {
  const priorityObj = priorities.find(p => p.value === priority);
  return priorityObj ? priorityObj.color : 'text-gray-600';
};

const PriorityDropdown = ({ value, onChange, error }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (priorityValue) => {
    onChange(priorityValue);
    setIsOpen(false);
  };

  const handleClear = (e) => {
    e.stopPropagation();
    onChange('');
    setIsOpen(false);
  };

  const selectedPriority = priorities.find(p => p.value === value);

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <div
        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between transition-colors duration-200 ${
          error ? 'border-red-500' : 'border-gray-300'
        } hover:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center space-x-2">
          {value && (
            <FaFlag 
              className={`${getPriorityColor(value)} transition-colors duration-200`} 
              size={16}
            />
          )}
          <span className={value ? 'text-gray-900' : 'text-gray-500'}>
            {selectedPriority ? selectedPriority.label : 'Select Priority'}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          {value && (
            <button
              type="button"
              onClick={handleClear}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
            >
              <FaTimes className="w-3 h-3 text-gray-400" />
            </button>
          )}
          <FaChevronDown 
            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg overflow-hidden">
          {priorities.map((priority) => (
            <button
              key={priority.value}
              type="button"
              onClick={() => handleSelect(priority.value)}
              className={`w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center space-x-2 transition-colors duration-200 ${
                value === priority.value ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
              }`}
            >
              <FaFlag 
                className={`${priority.color} transition-colors duration-200`} 
                size={16}
              />
              <span>{priority.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default PriorityDropdown;
