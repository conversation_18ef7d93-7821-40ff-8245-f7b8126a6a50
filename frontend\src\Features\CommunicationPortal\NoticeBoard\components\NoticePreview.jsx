import { useState } from 'react';
import { FaTimes, FaPaperPlane, FaSave, FaSpinner, FaImage, FaClock, FaFlag, FaTag } from 'react-icons/fa';

/**
 * NoticePreview Component
 * Shows a preview of the notice before submission
 */
const NoticePreview = ({ data, onClose, onSubmit, loading, isEdit = false }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const formatDateTime = (date, time) => {
    if (!date) return '';
    
    const dateObj = new Date(date);
    const formattedDate = dateObj.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    
    if (time) {
      const timeObj = new Date(`2000-01-01T${time}`);
      const formattedTime = timeObj.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
      return `${formattedDate} at ${formattedTime}`;
    }
    
    return formattedDate;
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAuthorName = () => {
    switch (data.postAs) {
      case 'group':
        return data.postedGroupName || 'Selected Group';
      case 'member':
        return data.postedMemberName || 'Selected Member';
      default:
        return 'You';
    }
  };

  const handleSubmit = () => {
    onSubmit(data);
  };

  const handleImageNavigation = (direction) => {
    if (!data.attachments || data.attachments.length === 0) return;
    
    if (direction === 'next') {
      setCurrentImageIndex((prev) => 
        prev === data.attachments.length - 1 ? 0 : prev + 1
      );
    } else {
      setCurrentImageIndex((prev) => 
        prev === 0 ? data.attachments.length - 1 : prev - 1
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Notice Preview
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            disabled={loading}
          >
            <FaTimes className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Priority and Label */}
          <div className="flex items-center gap-2 mb-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getPriorityColor(data.priority)}`}>
              <FaFlag className="w-3 h-3 inline mr-1" />
              {data.priority || 'Normal'}
            </span>
            {data.label && (
              <span className="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full border border-purple-200">
                <FaTag className="w-3 h-3 inline mr-1" />
                {data.label}
              </span>
            )}
          </div>

          {/* Images */}
          {data.attachments && data.attachments.length > 0 && (
            <div className="mb-6">
              <div className="relative">
                <img
                  src={data.attachments[currentImageIndex].preview}
                  alt={data.attachments[currentImageIndex].name}
                  className="w-full h-64 object-cover rounded-lg"
                />
                
                {/* Image Navigation */}
                {data.attachments.length > 1 && (
                  <>
                    <button
                      onClick={() => handleImageNavigation('prev')}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                    >
                      ←
                    </button>
                    <button
                      onClick={() => handleImageNavigation('next')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                    >
                      →
                    </button>
                    
                    {/* Image Counter */}
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                      {currentImageIndex + 1} / {data.attachments.length}
                    </div>
                  </>
                )}
              </div>
              
              {/* Image Thumbnails */}
              {data.attachments.length > 1 && (
                <div className="flex gap-2 mt-3 overflow-x-auto">
                  {data.attachments.map((attachment, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden ${
                        index === currentImageIndex ? 'border-blue-500' : 'border-gray-200'
                      }`}
                    >
                      <img
                        src={attachment.preview}
                        alt={attachment.name}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Date and Time Information */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
              <FaClock className="w-4 h-4" />
              Schedule
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div>
                <span className="font-medium">Start:</span> {formatDateTime(data.startDate, data.startTime)}
              </div>
              <div>
                <span className="font-medium">Expire:</span> {formatDateTime(data.endDate, data.endTime)}
              </div>
            </div>
          </div>

          {/* Author Information */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Posted By</h3>
            <div className="text-sm text-gray-700">
              <span className="font-medium">{getAuthorName()}</span>
              <span className="text-gray-500 ml-2">
                ({data.postAs === 'creator' ? 'Creator' : data.postAs === 'group' ? 'Group' : 'Member'})
              </span>
            </div>
          </div>

          {/* Target Audience */}
          <div className="bg-green-50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Target Audience</h3>
            <div className="text-sm text-gray-700">
              <div>
                <span className="font-medium">Towers:</span> {data.targetTowers?.length || 0} selected
              </div>
              <div>
                <span className="font-medium">Units:</span> {data.targetUnits?.length || 0} selected
              </div>
            </div>
          </div>

          {/* Attachments Summary */}
          {data.attachments && data.attachments.length > 0 && (
            <div className="bg-purple-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2 flex items-center gap-2">
                <FaImage className="w-4 h-4" />
                Images ({data.attachments.length})
              </h3>
              <div className="text-sm text-gray-700">
                {data.attachments.map((attachment, index) => (
                  <div key={index} className="truncate">
                    {attachment.name}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            disabled={loading}
          >
            Back to Edit
          </button>
          
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <FaSpinner className="w-4 h-4 animate-spin" />
            ) : isEdit ? (
              <FaSave className="w-4 h-4" />
            ) : (
              <FaPaperPlane className="w-4 h-4" />
            )}
            {loading ? (isEdit ? "Updating..." : "Creating...") : (isEdit ? "Update Notice" : "Create Notice")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NoticePreview;
