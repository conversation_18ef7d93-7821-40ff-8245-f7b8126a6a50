import { createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "../../../utils/axiosInstance";

// Get all notices with optional filters
export const fetchNotices = createAsyncThunk(
  "notices/fetchAll",
  async (params = {}, thunkAPI) => {
    try {
      const response = await axiosInstance.get('/api/noticeboard/notices/', { params });

      // Handle both paginated and direct array responses
      const noticesData = response.data.results || response.data;

      // Transform API response to match frontend structure if needed
      const transformedNotices = Array.isArray(noticesData)
        ? noticesData.map((notice) => ({
            id: notice.id,
            internalTitle: notice.internal_title,
            author: notice.post_as === "group"
              ? notice.group_name || "Unknown Group"
              : notice.post_as === "member"
              ? notice.member_name || "Unknown Member"
              : notice.creator_name || "Unknown Author",
            creatorName: notice.creator_name,
            priority: notice.priority,
            label: notice.label || "",
            startDate: notice.start_date,
            startTime: notice.start_time,
            endDate: notice.end_date,
            endTime: notice.end_time,
            status: notice.status,
            views: notice.views || 0,
            pinned: notice.is_pinned || false,
            isPinned: notice.is_pinned || false,
            manuallyExpired: notice.manually_expired || false,
            createdAt: notice.created_at,
            updatedAt: notice.updated_at,
            attachments: notice.attachments || [],
            postAs: notice.post_as || "creator",
            postedGroupName: notice.group_name,
            postedMemberName: notice.member_name,
            // Include target units and towers data for user count calculation
            target_units_data: notice.target_units_data || [],
            target_towers_data: notice.target_towers_data || [],
            editHistory: notice.history
              ? notice.history.map((historyEntry) => ({
                  editedBy: historyEntry.edited_by_name || "Unknown User",
                  timestamp: historyEntry.edited_at,
                  changes: historyEntry.changes || {}
                }))
              : []
          }))
        : [];

      return transformedNotices;
    } catch (error) {
      console.error('Error fetching notices:', error);
      return thunkAPI.rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Get single notice by ID
export const fetchNoticeById = createAsyncThunk(
  "notices/fetchById",
  async (id, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`/api/noticeboard/notices/${id}/`);

      // Transform the response to match the same structure as fetchNotices
      const notice = response.data;
      const transformedNotice = {
        id: notice.id,
        internalTitle: notice.internal_title,
        author: notice.post_as === "group"
          ? notice.group_name || "Unknown Group"
          : notice.post_as === "member"
          ? notice.member_name || "Unknown Member"
          : notice.creator_name || "Unknown Author",
        creatorName: notice.creator_name,
        priority: notice.priority,
        label: notice.label || "",
        startDate: notice.start_date,
        startTime: notice.start_time,
        endDate: notice.end_date,
        endTime: notice.end_time,
        status: notice.status,
        views: notice.views || 0,
        pinned: notice.is_pinned || false,
        isPinned: notice.is_pinned || false,
        manuallyExpired: notice.manually_expired || false,
        createdAt: notice.created_at,
        updatedAt: notice.updated_at,
        attachments: notice.attachments || [],
        postAs: notice.post_as || "creator",
        postedGroupName: notice.group_name,
        postedMemberName: notice.member_name,
        editHistory: notice.history
          ? notice.history.map((historyEntry) => ({
              editedBy: historyEntry.edited_by_name || "Unknown User",
              timestamp: historyEntry.edited_at,
              changes: historyEntry.changes || {}
            }))
          : [],
        // Include additional fields that might be needed for editing
        creator_name: notice.creator_name,
        posted_member: notice.posted_member,
        member_name: notice.member_name,
        posted_group: notice.posted_group,
        group_name: notice.group_name,
        target_towers_data: notice.target_towers_data || [],
        target_units_data: notice.target_units_data || [],
        start_date: notice.start_date,
        start_time: notice.start_time,
        end_date: notice.end_date,
        end_time: notice.end_time,
        post_as: notice.post_as
      };

      return transformedNotice;
    } catch (error) {
      console.error('Error fetching notice:', error);
      return thunkAPI.rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Create new notice
export const createNotice = createAsyncThunk(
  "notices/create",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.post('/api/noticeboard/notices/', data);

      // Transform the response to match the same structure as fetchNotices
      const notice = response.data;
      const transformedNotice = {
        id: notice.id,
        internalTitle: notice.internal_title,
        author: notice.post_as === "group"
          ? notice.group_name || "Unknown Group"
          : notice.post_as === "member"
          ? notice.member_name || "Unknown Member"
          : notice.creator_name || "Unknown Author",
        creatorName: notice.creator_name,
        priority: notice.priority,
        label: notice.label || "",
        startDate: notice.start_date,
        startTime: notice.start_time,
        endDate: notice.end_date,
        endTime: notice.end_time,
        status: notice.status,
        views: notice.views || 0,
        pinned: notice.is_pinned || false,
        isPinned: notice.is_pinned || false,
        manuallyExpired: notice.manually_expired || false,
        createdAt: notice.created_at,
        updatedAt: notice.updated_at,
        attachments: notice.attachments || [],
        postAs: notice.post_as || "creator",
        postedGroupName: notice.group_name,
        postedMemberName: notice.member_name,
        // Include target units and towers data for user count calculation
        target_units_data: notice.target_units_data || [],
        target_towers_data: notice.target_towers_data || [],
        editHistory: notice.history
          ? notice.history.map((historyEntry) => ({
              editedBy: historyEntry.edited_by_name || "Unknown User",
              timestamp: historyEntry.edited_at,
              changes: historyEntry.changes || {}
            }))
          : []
      };

      return transformedNotice;
    } catch (error) {
      console.error('Error creating notice:', error);
      return thunkAPI.rejectWithValue(error.response?.data || error.message);
    }
  }
);
