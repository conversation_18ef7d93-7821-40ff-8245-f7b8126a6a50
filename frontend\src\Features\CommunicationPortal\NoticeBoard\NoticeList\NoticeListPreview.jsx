import { useState } from "react";
import { FaEye, FaEdit, FaTrash, FaThumbtack, FaHistory, FaClock, FaCalendarAlt } from "react-icons/fa";
import { BiDotsVerticalRounded } from "react-icons/bi";
import { MdRestore } from "react-icons/md";
import NoticeActionMenu from "../components/NoticeActionMenu";
import NoticeAuthor from "../components/NoticeAuthor";
import NoticeInformation from "../components/NoticeInformation";
import { useUserCount } from "../hooks/useUserCount";

const NoticeListPreview = ({
  notices,
  loading,
  onEdit,
  onDelete,
  onExpire,
  onRestore,
  onPinToggle,
  onImageClick,
  onViewHistory,
  activeTab
}) => {
  const [openMenuId, setOpenMenuId] = useState(null);
  const { getUserCount } = useUserCount();

  const handleMenuToggle = (noticeId) => {
    setOpenMenuId(openMenuId === noticeId ? null : noticeId);
  };

  const handleMenuAction = (action, notice) => {
    setOpenMenuId(null);
    
    switch (action) {
      case 'edit':
        onEdit(notice.id);
        break;
      case 'delete':
        onDelete(notice);
        break;
      case 'expire':
        onExpire(notice);
        break;
      case 'restore':
        onRestore(notice);
        break;
      case 'pin':
      case 'unpin':
        onPinToggle(notice);
        break;
      case 'history':
        onViewHistory(notice);
        break;
      default:
        break;
    }
  };

  const formatDateTime = (date, time) => {
    if (!date) return '';
    
    const dateObj = new Date(date);
    const formattedDate = dateObj.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    
    if (time) {
      const timeObj = new Date(`2000-01-01T${time}`);
      const formattedTime = timeObj.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
      return `${formattedDate} at ${formattedTime}`;
    }
    
    return formattedDate;
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'ongoing':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {[...Array(8)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse">
            <div className="h-48 bg-gray-200 rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!notices || notices.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FaCalendarAlt className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No notices found</h3>
        <p className="text-gray-500 max-w-md">
          {activeTab === 1 && "There are no ongoing notices at the moment."}
          {activeTab === 2 && "There are no upcoming notices scheduled."}
          {activeTab === 3 && "There are no expired notices."}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {notices.map((notice) => {
        const userCount = getUserCount(notice.target_units_data || []);
        
        return (
          <div
            key={notice.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 flex flex-col"
          >
            {/* Header with Pin and Menu */}
            <div className="flex justify-between items-start p-4 pb-2">
              <div className="flex items-center gap-2">
                {notice.isPinned && (
                  <FaThumbtack className="w-4 h-4 text-red-500 transform rotate-45" />
                )}
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(notice.priority)}`}>
                  {notice.priority || 'Normal'}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(notice.status)}`}>
                  {notice.status || 'Unknown'}
                </span>
              </div>
              
              <div className="relative">
                <button
                  onClick={() => handleMenuToggle(notice.id)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <BiDotsVerticalRounded className="w-5 h-5 text-gray-500" />
                </button>
                
                {openMenuId === notice.id && (
                  <NoticeActionMenu
                    notice={notice}
                    onAction={handleMenuAction}
                    onClose={() => setOpenMenuId(null)}
                    activeTab={activeTab}
                  />
                )}
              </div>
            </div>

            {/* Images */}
            <div className="px-4 pb-2">
              {notice.attachments && notice.attachments.length > 0 ? (
                <div className="grid grid-cols-2 gap-2">
                  {notice.attachments.slice(0, 4).map((attachment, index) => (
                    <div
                      key={attachment.id}
                      className="relative cursor-pointer group"
                      onClick={() => onImageClick(
                        notice.attachments.map(att => ({ url: att.file_url, alt: att.file_name })),
                        index
                      )}
                    >
                      <img
                        src={attachment.file_url}
                        alt={attachment.file_name}
                        className="w-full h-24 object-cover rounded-lg group-hover:opacity-90 transition-opacity"
                      />
                      {index === 3 && notice.attachments.length > 4 && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                          <span className="text-white font-medium">
                            +{notice.attachments.length - 4} more
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                  <FaCalendarAlt className="w-8 h-8 text-gray-400" />
                </div>
              )}
            </div>

            {/* Content */}
            <div className="px-4 pb-2 flex-1">
              {/* Internal Title (if available) */}
              {notice.internalTitle && (
                <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">
                  {notice.internalTitle}
                </h3>
              )}

              {/* Label */}
              {notice.label && (
                <span className="inline-block px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full mb-2">
                  {notice.label}
                </span>
              )}
            </div>

            {/* Author */}
            <div className="px-4 pb-2">
              <NoticeAuthor notice={notice} />
            </div>

            {/* Date/Time Information */}
            <div className="px-4 pb-2">
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <FaClock className="w-3 h-3" />
                    Start: {formatDateTime(notice.startDate, notice.startTime)}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <FaClock className="w-3 h-3" />
                    Expire: {formatDateTime(notice.endDate, notice.endTime)}
                  </span>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-4 py-3 border-t border-gray-100 bg-gray-50 rounded-b-lg">
              <NoticeInformation notice={notice} userCount={userCount} />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default NoticeListPreview;
