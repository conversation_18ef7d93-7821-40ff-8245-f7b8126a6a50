import { useDispatch } from 'react-redux';
import { togglePinNotice } from '../../../../redux/slices/api/noticeApi';

/**
 * Custom hook for pin/unpin functionality
 */
const usePinPost = () => {
  const dispatch = useDispatch();

  const pinPost = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error pinning notice:', error);
      return { success: false, error };
    }
  };

  const unpinPost = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error unpinning notice:', error);
      return { success: false, error };
    }
  };

  const togglePin = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error toggling pin status:', error);
      return { success: false, error };
    }
  };

  return {
    pinPost,
    unpinPost,
    togglePin
  };
};

export default usePinPost;
